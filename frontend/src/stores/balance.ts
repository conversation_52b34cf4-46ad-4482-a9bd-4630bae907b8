import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'
import websocketService from '@/services/websocket'
import { useAuthStore } from './auth'

interface Balance {
  currency_code: string
  balance: number
  formatted: number
  symbol: string
  decimals: number
  sequence?: number // Track the last sequence number for this balance
}

export const useBalanceStore = defineStore('balance', () => {
  const balances = ref<Balance[]>([])
  const selectedCurrency = ref<string>('')
  const isLoading = ref(false)
  const isInitialized = ref(false)

  // Get auth store to access user data
  const authStore = useAuthStore()

  // WebSocket callback reference for proper cleanup
  const balanceUpdateCallback = (data: any) => {
    console.log('Balance update received:', data)
    updateBalanceWithSequence(data.currency_code, data.balance, data.formatted, data.sequence)
  }

  // Computed properties
  const currentBalance = computed(() => {
    if (!selectedCurrency.value || balances.value.length === 0) {
      return null
    }
    return balances.value.find(b => b.currency_code === selectedCurrency.value) || null
  })

  const availableCurrencies = computed(() => {
    return balances.value.map(b => ({
      code: b.currency_code,
      symbol: b.symbol,
      formatted: b.formatted,
      decimals: b.decimals
    }))
  })

  // Actions
  async function fetchBalances() {
    if (!authStore.isAuthenticated) {
      return
    }

    try {
      isLoading.value = true
      const response = await axios.get(`${import.meta.env.VITE_API_URL}/api/user/balance`)

      if (response.data.success) {
        balances.value = response.data.data

        // Set default currency if not already set
        if (!selectedCurrency.value) {
          setDefaultCurrency()
        }

        isInitialized.value = true
      }
    } catch (error) {
      console.error('Failed to fetch balances:', error)
    } finally {
      isLoading.value = false
    }
  }

  function setDefaultCurrency() {
    // Check localStorage first
    const stored = localStorage.getItem('selectedCurrency')
    if (stored && balances.value.some(b => b.currency_code === stored)) {
      selectedCurrency.value = stored
      return
    }

    // Default logic: POINTS if available, else first available currency
    const pointsBalance = balances.value.find(b => b.currency_code === 'POINTS')
    if (pointsBalance) {
      selectedCurrency.value = 'POINTS'
    } else if (balances.value.length > 0) {
      selectedCurrency.value = balances.value[0].currency_code
    } else {
      // Fallback if no balances exist - show 0 POINTS
      selectedCurrency.value = 'POINTS'
      // Add a default POINTS balance with 0 value
      balances.value.push({
        currency_code: 'POINTS',
        balance: 0,
        formatted: 0,
        symbol: 'PTS',
        decimals: 0
      })
    }

    // Store the selection
    localStorage.setItem('selectedCurrency', selectedCurrency.value)
  }

  function selectCurrency(currencyCode: string) {
    selectedCurrency.value = currencyCode
    localStorage.setItem('selectedCurrency', currencyCode)
  }

  function updateBalance(currencyCode: string, balance: number, formatted: number) {
    const existingBalance = balances.value.find(b => b.currency_code === currencyCode)
    if (existingBalance) {
      existingBalance.balance = balance
      existingBalance.formatted = formatted
      // Don't update sequence for manual updates (API responses)
    } else {
      // Add new balance if it doesn't exist
      balances.value.push({
        currency_code: currencyCode,
        balance,
        formatted,
        symbol: currencyCode, // Fallback symbol
        decimals: currencyCode === 'POINTS' ? 0 : currencyCode === 'USDT' ? 6 : 2 // Fallback decimals
      })
    }
  }

  function updateBalanceWithSequence(currencyCode: string, balance: number, formatted: number, sequence?: number) {
    const existingBalance = balances.value.find(b => b.currency_code === currencyCode)

    if (existingBalance) {
      // Check if this update is newer than the last one we processed
      if (sequence !== undefined && existingBalance.sequence !== undefined && sequence <= existingBalance.sequence) {
        console.log(`Ignoring out-of-order balance update for ${currencyCode}. Received sequence: ${sequence}, current: ${existingBalance.sequence}`)
        return
      }

      existingBalance.balance = balance
      existingBalance.formatted = formatted
      if (sequence !== undefined) {
        existingBalance.sequence = sequence
      }
    } else {
      // Add new balance if it doesn't exist
      balances.value.push({
        currency_code: currencyCode,
        balance,
        formatted,
        symbol: currencyCode, // Fallback symbol
        decimals: currencyCode === 'POINTS' ? 0 : currencyCode === 'USDT' ? 6 : 2, // Fallback decimals
        sequence: sequence
      })
    }
  }

  function initializeWebSocket() {
    if (!authStore.isAuthenticated || !authStore.user) {
      return
    }

    try {
      // Subscribe to balance updates through the centralized WebSocket service
      websocketService.subscribe('balance.updated', balanceUpdateCallback)
    } catch (error) {
      console.error('Failed to initialize balance WebSocket:', error)
    }
  }

  function cleanupWebSocket() {
    try {
      // Unsubscribe from balance updates
      websocketService.unsubscribe('balance.updated', balanceUpdateCallback)
    } catch (error) {
      console.error('Failed to cleanup balance WebSocket:', error)
    }
  }

  async function initialize() {
    if (!authStore.isAuthenticated) {
      reset()
      return
    }

    await fetchBalances()
    initializeWebSocket()
  }

  function reset() {
    balances.value = []
    selectedCurrency.value = ''
    isLoading.value = false
    isInitialized.value = false
    cleanupWebSocket()
  }

  // Update authentication state
  function updateAuthenticationState(isAuth: boolean) {
    if (!isAuth) {
      reset()
    } else {
      initialize()
    }
  }

  return {
    // State
    balances,
    selectedCurrency,
    isLoading,
    isInitialized,

    // Computed
    currentBalance,
    availableCurrencies,

    // Actions
    fetchBalances,
    selectCurrency,
    updateBalance,
    updateBalanceWithSequence,
    initialize,
    reset,
    updateAuthenticationState,
    initializeWebSocket,
    cleanupWebSocket
  }
})
