// Simple test to demonstrate balance sequence functionality
// This can be run in the browser console to test the sequence logic

// Mock balance store functionality
const balances = []

function updateBalanceWithSequence(currencyCode, balance, formatted, sequence) {
  const existingBalance = balances.find(b => b.currency_code === currencyCode)
  
  if (existingBalance) {
    // Check if this update is newer than the last one we processed
    if (sequence !== undefined && existingBalance.sequence !== undefined && sequence <= existingBalance.sequence) {
      console.log(`Ignoring out-of-order balance update for ${currencyCode}. Received sequence: ${sequence}, current: ${existingBalance.sequence}`)
      return false // Update was ignored
    }
    
    existingBalance.balance = balance
    existingBalance.formatted = formatted
    if (sequence !== undefined) {
      existingBalance.sequence = sequence
    }
  } else {
    // Add new balance if it doesn't exist
    balances.push({
      currency_code: currencyCode,
      balance,
      formatted,
      sequence: sequence
    })
  }
  
  console.log(`Balance updated for ${currencyCode}: ${formatted} (sequence: ${sequence})`)
  return true // Update was applied
}

// Test sequence functionality
console.log('Testing balance sequence functionality...')

// Simulate normal sequence of updates
console.log('\n1. Normal sequence (should all be applied):')
updateBalanceWithSequence('USD', 10000, 100.00, 1)
updateBalanceWithSequence('USD', 9000, 90.00, 2)
updateBalanceWithSequence('USD', 8500, 85.00, 3)

console.log('Current balances:', balances)

// Simulate out-of-order update (should be ignored)
console.log('\n2. Out-of-order update (should be ignored):')
const ignored = updateBalanceWithSequence('USD', 9500, 95.00, 2) // Sequence 2 is older than current 3
console.log('Update was ignored:', !ignored)

console.log('Current balances after out-of-order attempt:', balances)

// Simulate newer update (should be applied)
console.log('\n3. Newer update (should be applied):')
updateBalanceWithSequence('USD', 8000, 80.00, 4)

console.log('Final balances:', balances)

console.log('\nTest completed! The sequence logic correctly ignores out-of-order updates.')
