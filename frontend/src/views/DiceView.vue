<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useBalanceStore } from '@/stores/balance'
import { useDiceStore } from '@/stores/dice'

const authStore = useAuthStore()
const balanceStore = useBalanceStore()
const diceStore = useDiceStore()

// Game state
const betAmount = ref<number>(1.00)
const threshold = ref<number>(50.00)
const direction = ref<'over' | 'under'>('over')
const isRolling = ref<boolean>(false)

// Auto-bet state
const isAutoBetting = ref<boolean>(false)
const autoBetRolls = ref<number>(10)
const autoBetMode = ref<'manual' | 'auto'>('manual')

// Computed values
const winChance = computed(() => {
  if (direction.value === 'over') {
    return 100.0 - threshold.value
  } else {
    return threshold.value
  }
})

const multiplier = computed(() => {
  if (winChance.value <= 0) return 0
  const houseEdge = diceStore.config?.house_edge || 1.0
  return (100.0 - houseEdge) / winChance.value
})

const estimatedProfit = computed(() => {
  return betAmount.value * multiplier.value - betAmount.value
})

const thresholdPercent = computed(() => {
  return `${threshold.value}%`
})

const maxBet = computed(() => {
  if (!balanceStore.currentBalance) return 0
  return balanceStore.currentBalance.formatted
})

const minBet = computed(() => {
  return diceStore.minimumBetInfo?.minimum_bet || 0.01
})

const betStep = computed(() => {
  const decimals = balanceStore.currentBalance?.decimals || 2
  return 1 / Math.pow(10, decimals)
})

const canRoll = computed(() => {
  const minThreshold = diceStore.config?.min_threshold || 2.00
  const maxThreshold = diceStore.config?.max_threshold || 98.00

  return betAmount.value >= minBet.value &&
         betAmount.value <= maxBet.value &&
         threshold.value >= minThreshold &&
         threshold.value <= maxThreshold &&
         winChance.value > 0 &&
         !isRolling.value
})



// Methods

const roll = async () => {
  if (!canRoll.value) return

  isRolling.value = true

  try {
    // Get currency ID from the currencies API
    let currencyId = null
    if (balanceStore.currentBalance) {
      const currenciesResponse = await fetch(`${import.meta.env.VITE_API_URL}/api/currencies`)
      const currenciesData = await currenciesResponse.json()

      if (currenciesData.success) {
        const currency = currenciesData.data.find((c: any) => c.code === balanceStore.currentBalance?.currency_code)
        currencyId = currency?.id
      }
    }

    await diceStore.roll({
      bet: betAmount.value,
      threshold: threshold.value,
      direction: direction.value,
      currency_id: currencyId
    })
  } catch (error) {
    console.error('Roll failed:', error)
  } finally {
    isRolling.value = false
  }
}

const setMaxBet = () => {
  betAmount.value = maxBet.value
}

const halveBet = () => {
  const newAmount = betAmount.value / 2
  betAmount.value = Math.max(newAmount, minBet.value)
}

const doubleBet = () => {
  const newAmount = betAmount.value * 2
  betAmount.value = Math.min(newAmount, maxBet.value)
}

const rollDice = roll

const canBet = computed(() => {
  return canRoll.value && !isAutoBetting.value
})

// Input validation methods
const formatBetAmount = () => {
  const decimals = balanceStore.currentBalance?.decimals || 2
  betAmount.value = parseFloat(betAmount.value.toFixed(decimals))
}

const formatAutoBetRolls = () => {
  autoBetRolls.value = Math.floor(Math.max(1, autoBetRolls.value))
}

// Auto-betting methods
const startAutoBet = async () => {
  if (autoBetRolls.value <= 0 || isAutoBetting.value) return

  isAutoBetting.value = true

  await performAutoBetSequence()
}

const stopAutoBet = () => {
  isAutoBetting.value = false
}

const performAutoBetSequence = async () => {
  while (isAutoBetting.value && autoBetRolls.value > 0) {
    // Check if we have enough balance
    if (betAmount.value > maxBet.value) {
      console.log('Auto-bet stopped: Insufficient balance')
      stopAutoBet()
      break
    }

    try {
      await rollDice()
      autoBetRolls.value--

      // Small delay between rolls for better UX
      if (autoBetRolls.value > 0 && isAutoBetting.value) {
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    } catch (error) {
      console.error('Auto-bet roll failed:', error)
      stopAutoBet()
      break
    }
  }

  // Auto-bet sequence completed
  if (autoBetRolls.value === 0) {
    isAutoBetting.value = false
  }
}

// Watch for threshold changes to ensure valid range
watch(threshold, (newThreshold) => {
  const minThreshold = diceStore.config?.min_threshold || 2.00
  const maxThreshold = diceStore.config?.max_threshold || 98.00

  // Constrain threshold to valid range
  if (newThreshold < minThreshold) {
    threshold.value = minThreshold
  } else if (newThreshold > maxThreshold) {
    threshold.value = maxThreshold
  }
})

// Watch for direction changes to ensure valid win chance
watch(direction, () => {
  const minThreshold = diceStore.config?.min_threshold || 2.00
  const maxThreshold = diceStore.config?.max_threshold || 98.00

  if (winChance.value <= 0) {
    if (direction.value === 'over') {
      threshold.value = maxThreshold
    } else {
      threshold.value = minThreshold
    }
  }
})



const fetchMinimumBetInfo = async () => {
  if (!balanceStore.currentBalance) return

  try {
    // Get currency ID from the currencies API
    const currenciesResponse = await fetch(`${import.meta.env.VITE_API_URL}/api/currencies`)
    const currenciesData = await currenciesResponse.json()

    if (currenciesData.success) {
      const currency = currenciesData.data.find((c: any) => c.code === balanceStore.currentBalance?.currency_code)
      if (currency?.id) {
        await diceStore.getMinimumBet({ currency_id: currency.id })
      }
    }
  } catch (error) {
    console.error('Failed to fetch minimum bet info:', error)
  }
}

onMounted(async () => {
  // Initialize balance store if needed
  if (!balanceStore.isInitialized) {
    await balanceStore.fetchBalances()
  }

  // If still no current balance, try to initialize again
  if (!balanceStore.currentBalance && authStore.isAuthenticated) {
    await balanceStore.initialize()
  }

  // Fetch dice game config
  await diceStore.getConfig()

  // Fetch minimum bet info for current currency
  await fetchMinimumBetInfo()

  // Initialize dice store WebSocket
  diceStore.initialize()
})

// Watch for currency changes and update minimum bet info
watch(() => balanceStore.currentBalance?.currency_code, async () => {
  await fetchMinimumBetInfo()
})
</script>

<template>
  <div class="dice-game">
    <div class="game-container">
      <div class="game-content">
        <!-- Mode Toggle -->
        <div class="mode-toggle">
          <button
            @click="autoBetMode = 'manual'"
            :class="{ active: autoBetMode === 'manual' }"
            class="mode-btn"
            :disabled="isAutoBetting"
          >
            Manual
          </button>
          <button
            @click="autoBetMode = 'auto'"
            :class="{ active: autoBetMode === 'auto' }"
            class="mode-btn"
            :disabled="isAutoBetting"
          >
            Auto
          </button>
        </div>

        <!-- Left Panel -->
        <div class="left-panel">
          <!-- Bet Amount Section -->
          <div class="bet-section">
            <label class="section-label">Bet Amount</label>
            <div class="bet-amount-input-container">
              <input
                v-model.number="betAmount"
                type="number"
                :min="minBet"
                :max="maxBet"
                :step="betStep"
                class="bet-amount-input"
                :disabled="isRolling || isAutoBetting"
                @blur="formatBetAmount"
                @input="formatBetAmount"
              />
              <span class="currency-symbol">{{ balanceStore.currentBalance?.currency_code || 'USD' }}</span>
            </div>
            <div class="bet-controls">
              <button @click="halveBet" class="bet-control-btn" :disabled="isRolling || isAutoBetting">
                <span class="bet-icon">½</span>
              </button>
              <button @click="doubleBet" class="bet-control-btn" :disabled="isRolling || isAutoBetting">
                <span class="bet-icon">2x</span>
              </button>
              <button @click="setMaxBet" class="bet-control-btn" :disabled="isRolling || isAutoBetting">
                Max
              </button>
            </div>
          </div>

          <!-- Profit on Win -->
          <div class="profit-section">
            <label class="section-label">Profit on Win</label>
            <div class="profit-display">
              {{ estimatedProfit.toFixed(balanceStore.currentBalance?.decimals || 2) }} {{ balanceStore.currentBalance?.currency_code || 'USD' }}
            </div>
          </div>

          <!-- Manual Mode Controls -->
          <div v-if="autoBetMode === 'manual'" class="manual-controls">
            <button
              @click="rollDice"
              class="bet-button"
              :disabled="isRolling || !canBet"
              :class="{ rolling: isRolling }"
            >
              {{ isRolling ? 'Rolling...' : 'Bet' }}
            </button>
          </div>

          <!-- Auto Mode Controls -->
          <div v-else class="auto-controls">
            <div class="auto-bet-input">
              <label class="section-label">Number of Rolls</label>
              <input
                v-model.number="autoBetRolls"
                type="number"
                min="1"
                max="1000"
                step="1"
                class="auto-rolls-input"
                :disabled="isAutoBetting"
                @blur="formatAutoBetRolls"
                @input="formatAutoBetRolls"
              />
            </div>

            <button
              v-if="isAutoBetting"
              @click="stopAutoBet"
              class="stop-button"
            >
              Stop Auto Bet
            </button>

            <button
              v-else
              @click="startAutoBet"
              class="bet-button auto-bet-button"
              :disabled="isRolling || !canBet || autoBetRolls <= 0"
            >
              Start Auto Bet
            </button>
          </div>
        </div>

        <!-- Right Panel -->
        <div class="right-panel">
          <!-- Slider Section -->
          <div class="slider-section">
            <div class="slider-container">
              <input
                id="threshold"
                v-model.number="threshold"
                type="range"
                min="0"
                max="100"
                step="1"
                class="threshold-slider"
                :class="{ 'over-direction': direction === 'over', 'under-direction': direction === 'under' }"
                :style="{ '--threshold-percent': thresholdPercent }"
                :disabled="isRolling"
              />
              <!-- Dice Result Indicator -->
              <div
                v-if="diceStore.lastResult"
                class="dice-result-indicator"
                :class="{ 'win': diceStore.lastResult.win, 'loss': !diceStore.lastResult.win }"
                :style="{ left: `${diceStore.lastResult.roll}%` }"
              >
                <div class="dice-result-value">{{ diceStore.lastResult.roll }}</div>
              </div>
              <div class="slider-markers">
                <span>0</span>
                <span>25</span>
                <span>50</span>
                <span>75</span>
                <span>100</span>
              </div>
            </div>
          </div>

          <!-- Roll History -->
          <div class="history-section">
            <label class="section-label">Recent Rolls</label>
            <div class="roll-history">
              <div
                v-for="roll in diceStore.gameHistory"
                :key="roll.id"
                class="history-item"
                :class="{ 'win': roll.win, 'loss': !roll.win }"
                :title="`Roll: ${roll.roll} | ${roll.win ? 'Won' : 'Lost'} | Profit: ${roll.profit_formatted}`"
              >
                {{ roll.roll }}
              </div>
              <div v-if="diceStore.gameHistory.length === 0" class="no-history">
                No rolls yet
              </div>
            </div>
          </div>

          <!-- Direction Toggle -->
          <div class="direction-section">
            <div class="direction-toggle">
              <button
                @click="direction = 'over'"
                :class="{ active: direction === 'over' }"
                class="direction-btn"
                :disabled="isRolling || isAutoBetting"
              >
                Over
              </button>
              <button
                @click="direction = 'under'"
                :class="{ active: direction === 'under' }"
                class="direction-btn"
                :disabled="isRolling || isAutoBetting"
              >
                Under
              </button>
            </div>
          </div>

          <!-- Stats Section -->
          <div class="bottom-stats">
            <div class="stat-group">
              <label class="stat-label">Multiplier</label>
              <div class="stat-value">{{ multiplier.toFixed(2) }}x</div>
            </div>
            <div class="stat-group">
              <label class="stat-label">Roll {{ direction === 'over' ? 'Over' : 'Under' }}</label>
              <div class="stat-value">{{ threshold.toFixed(2) }}</div>
            </div>
            <div class="stat-group">
              <label class="stat-label">Win Chance</label>
              <div class="stat-value">{{ winChance.toFixed(2) }}%</div>
            </div>
          </div>
        </div>


      </div>
    </div>
  </div>
</template>

<style scoped>
.dice-game {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.game-container {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  color: #1f2937;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.game-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  align-items: start;
}

/* Mode Toggle */
.mode-toggle {
  grid-column: 1 / -1;
  display: flex;
  background: #f1f5f9;
  border-radius: 8px;
  padding: 4px;
  margin-bottom: 2rem;
  width: fit-content;
}

.mode-btn {
  padding: 8px 24px;
  border: none;
  background: transparent;
  color: #64748b;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.mode-btn.active {
  background: #3b82f6;
  color: white;
}

.mode-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Left Panel */
.left-panel {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.bet-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
}

.section-label {
  display: block;
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 1rem;
}

.bet-amount-input-container {
  position: relative;
  margin-bottom: 1rem;
}

.bet-amount-input {
  width: 100%;
  padding: 0.75rem 4rem 0.75rem 0.75rem;
  font-size: 1.5rem;
  font-weight: bold;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #1f2937;
  outline: none;
  transition: border-color 0.2s;
}

.bet-amount-input:focus {
  border-color: #3b82f6;
}

.bet-amount-input:disabled {
  background: #f8fafc;
  opacity: 0.7;
}

.currency-symbol {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1rem;
  font-weight: 600;
  color: #64748b;
  pointer-events: none;
}

.bet-controls {
  display: flex;
  gap: 0.5rem;
}

.bet-control-btn {
  flex: 1;
  padding: 0.75rem;
  background: #e2e8f0;
  border: none;
  border-radius: 8px;
  color: #374151;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.bet-control-btn:hover:not(:disabled) {
  background: #cbd5e1;
}

.bet-control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.bet-icon {
  font-size: 1.125rem;
  font-weight: bold;
}

.profit-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
}

.profit-display {
  font-size: 1.25rem;
  font-weight: bold;
  color: #1f2937;
}

.bet-button {
  padding: 1rem 2rem;
  background: #22c55e;
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1.125rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
}

.bet-button:hover:not(:disabled) {
  background: #16a34a;
  transform: translateY(-1px);
}

.bet-button:disabled {
  background: #6b7280;
  cursor: not-allowed;
  transform: none;
}

.bet-button.rolling {
  background: #f59e0b;
}

/* Auto-bet Controls */
.auto-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.auto-bet-input {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1rem;
}

.auto-rolls-input {
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  font-weight: 600;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #1f2937;
  outline: none;
  transition: border-color 0.2s;
}

.auto-rolls-input:focus {
  border-color: #3b82f6;
}

.auto-rolls-input:disabled {
  background: #f8fafc;
  opacity: 0.7;
}

.stop-button {
  padding: 1rem 2rem;
  background: #dc2626;
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
}

.stop-button:hover {
  background: #b91c1c;
  transform: translateY(-1px);
}

.auto-bet-button {
  background: #f59e0b;
}

.auto-bet-button:hover:not(:disabled) {
  background: #d97706;
}

/* Right Panel */
.right-panel {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Direction Section */
.direction-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
}

.direction-toggle {
  display: flex;
  background: #e2e8f0;
  border-radius: 8px;
  padding: 4px;
}

.direction-btn {
  flex: 1;
  padding: 0.75rem 1.5rem;
  border: none;
  background: transparent;
  color: #64748b;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
}

.direction-btn.active {
  background: #3b82f6;
  color: white;
}

.direction-btn:hover:not(:disabled):not(.active) {
  background: #cbd5e1;
  color: #374151;
}

.direction-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Slider Section */
.slider-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 1rem;
}

.slider-container {
  position: relative;
  margin: 2rem 0;
}

/* Dice Result Indicator */
.dice-result-indicator {
  position: absolute;
  top: -15px;
  width: 3px;
  height: 50px;
  transform: translateX(-50%);
  z-index: 3;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.dice-result-indicator.win {
  background: #22c55e;
  box-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
}

.dice-result-indicator.loss {
  background: #dc2626;
  box-shadow: 0 0 10px rgba(220, 38, 38, 0.5);
}

.dice-result-value {
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 2px solid;
  border-radius: 8px;
  padding: 4px 8px;
  font-size: 0.875rem;
  font-weight: bold;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dice-result-indicator.win .dice-result-value {
  border-color: #22c55e;
  color: #22c55e;
}

.dice-result-indicator.loss .dice-result-value {
  border-color: #dc2626;
  color: #dc2626;
}

.threshold-slider {
  width: 100%;
  height: 20px;
  border-radius: 10px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
  background: #e2e8f0;
  position: relative;
  z-index: 1;
}

.threshold-slider::-webkit-slider-track {
  height: 20px;
  border-radius: 10px;
  background: #e2e8f0;
}

.threshold-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 40px;
  width: 40px;
  border-radius: 8px;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 2;
}

.threshold-slider::-moz-range-track {
  height: 20px;
  border-radius: 10px;
  border: none;
  background: #e2e8f0;
}

.threshold-slider::-moz-range-thumb {
  height: 40px;
  width: 40px;
  border-radius: 8px;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border: none;
}

.slider-markers {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
  padding: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  position: relative;
  margin-left: 25px;
  margin-right: 25px;
}

.slider-markers span {
  position: absolute;
  transform: translateX(-50%);
}

.slider-markers span:nth-child(1) { left: 0%; }
.slider-markers span:nth-child(2) { left: 25%; }
.slider-markers span:nth-child(3) { left: 50%; }
.slider-markers span:nth-child(4) { left: 75%; }
.slider-markers span:nth-child(5) { left: 100%; }

/* Roll History */
.history-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
}

.roll-history {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.history-item {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 50px;
  text-align: center;
  background: #e2e8f0;
  color: #64748b;
}

.history-item.win {
  background: #22c55e;
  color: white;
}

.history-item.loss {
  background: #64748b;
  color: white;
}

.history-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.no-history {
  color: #94a3b8;
  font-style: italic;
  text-align: center;
  padding: 1rem;
}

/* Over direction: green on right (winning side), red on left (losing side) */
.threshold-slider.over-direction {
  background: linear-gradient(to right, #dc2626 0%, #dc2626 var(--threshold-percent), #22c55e var(--threshold-percent), #22c55e 100%);
}

.threshold-slider.over-direction::-webkit-slider-track {
  background: linear-gradient(to right, #dc2626 0%, #dc2626 var(--threshold-percent), #22c55e var(--threshold-percent), #22c55e 100%);
}

.threshold-slider.over-direction::-moz-range-track {
  background: linear-gradient(to right, #dc2626 0%, #dc2626 var(--threshold-percent), #22c55e var(--threshold-percent), #22c55e 100%);
}

/* Under direction: green on left (winning side), red on right (losing side) */
.threshold-slider.under-direction {
  background: linear-gradient(to right, #22c55e 0%, #22c55e var(--threshold-percent), #dc2626 var(--threshold-percent), #dc2626 100%);
}

.threshold-slider.under-direction::-webkit-slider-track {
  background: linear-gradient(to right, #22c55e 0%, #22c55e var(--threshold-percent), #dc2626 var(--threshold-percent), #dc2626 100%);
}

.threshold-slider.under-direction::-moz-range-track {
  background: linear-gradient(to right, #22c55e 0%, #22c55e var(--threshold-percent), #dc2626 var(--threshold-percent), #dc2626 100%);
}

/* Bottom Stats */
.bottom-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
}

.stat-group {
  text-align: center;
}

.stat-label {
  display: block;
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: bold;
  color: #1f2937;
}



/* Responsive Design */
@media (max-width: 1024px) {
  .game-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .mode-toggle {
    justify-self: center;
  }
}

@media (max-width: 768px) {
  .dice-game {
    padding: 1rem;
  }

  .game-container {
    padding: 1rem;
  }

  .bottom-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
</style>
