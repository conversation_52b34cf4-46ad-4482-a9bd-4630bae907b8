<?php

namespace App\Services;

use App\Models\Currency;
use App\Models\Transaction;
use App\Models\User;
use App\Models\UserBalance;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BalanceService
{
    /**
     * Sequence counter for balance updates to prevent out-of-order WebSocket updates.
     * This is stored in cache with a TTL to handle server restarts gracefully.
     */
    private function getNextSequence(int $userId, int $currencyId): int
    {
        $key = "balance_sequence_{$userId}_{$currencyId}";
        $sequence = cache()->increment($key);

        // Set TTL of 1 hour for the sequence counter
        cache()->put($key, $sequence, 3600);

        return $sequence;
    }

    /**
     * Credit a user's balance (add funds).
     */
    public function credit(
        User $user,
        Currency $currency,
        int $amount,
        string $type,
        ?string $referenceId = null,
        ?array $meta = null
    ): Transaction {
        if ($amount <= 0) {
            throw new \InvalidArgumentException('Credit amount must be positive');
        }

        return $this->processTransaction($user, $currency, $amount, $type, $referenceId, $meta);
    }

    /**
     * Debit a user's balance (subtract funds).
     */
    public function debit(
        User $user,
        Currency $currency,
        int $amount,
        string $type,
        ?string $referenceId = null,
        ?array $meta = null
    ): Transaction {
        if ($amount <= 0) {
            throw new \InvalidArgumentException('Debit amount must be positive');
        }

        return $this->processTransaction($user, $currency, -$amount, $type, $referenceId, $meta);
    }

    /**
     * Get user balance for a specific currency (returns numeric balance).
     */
    public function getBalance(User $user, Currency $currency): int
    {
        $balance = UserBalance::where('user_id', $user->id)
            ->where('currency_id', $currency->id)
            ->first();

        return $balance ? $balance->balance : 0;
    }

    /**
     * Get all balances for a user grouped by currency.
     */
    public function getBalances(User $user): array
    {
        $balances = UserBalance::where('user_id', $user->id)
            ->with('currency')
            ->get();

        $result = [];
        foreach ($balances as $balance) {
            $result[$balance->currency->code] = $balance->balance;
        }

        return $result;
    }

    /**
     * Start balance chain for a user - creates user_balances entries with balance = 0
     * and genesis transactions with amount = 0 for all currencies.
     */
    public function startBalanceChain(User $user): array
    {
        $currencies = Currency::all();
        $results = [];

        foreach ($currencies as $currency) {
            $results[$currency->code] = DB::transaction(function () use ($user, $currency) {
                // Check if balance already exists
                $existingBalance = UserBalance::where('user_id', $user->id)
                    ->where('currency_id', $currency->id)
                    ->first();

                if ($existingBalance) {
                    return $existingBalance;
                }

                // Create genesis transaction with amount = 0
                $genesisTransaction = $this->createGenesisTransaction($user, $currency);

                // Create initial balance record
                $balance = new UserBalance([
                    'user_id' => $user->id,
                    'currency_id' => $currency->id,
                    'balance' => 0,
                    'updated_at' => now(),
                ]);

                // Generate balance hash
                $balance->balance_hash = $this->generateBalanceHash(
                    $user->id,
                    $currency->id,
                    0,
                    $genesisTransaction->id,
                    $genesisTransaction->hash
                );

                $balance->save();

                return $balance;
            });
        }

        return $results;
    }

    /**
     * Create a genesis transaction with amount = 0.
     */
    private function createGenesisTransaction(User $user, Currency $currency): Transaction
    {
        $previousHash = hash('sha256', "genesis_{$user->id}_{$currency->id}");
        $createdAt = now();

        $transaction = new Transaction([
            'user_id' => $user->id,
            'currency_id' => $currency->id,
            'amount' => 0,
            'balance_before' => 0,
            'balance_after' => 0,
            'type' => 'genesis',
            'reference_id' => null,
            'meta' => null,
            'previous_hash' => $previousHash,
            'created_at' => $createdAt,
        ]);

        // Generate transaction hash
        $transaction->hash = $this->generateTransactionHash(
            $user->id,
            $currency->id,
            0,
            0,
            0,
            'genesis',
            null,
            null,
            $createdAt,
            $previousHash
        );

        $transaction->save();

        return $transaction;
    }

    /**
     * Process a balance change transaction atomically.
     */
    private function processTransaction(
        User $user,
        Currency $currency,
        int $amount,
        string $type,
        ?string $referenceId = null,
        ?array $meta = null
    ): Transaction {
        return DB::transaction(function () use ($user, $currency, $amount, $type, $referenceId, $meta) {
            // Lock the user balance for update to prevent race conditions
            $balance = UserBalance::where('user_id', $user->id)
                ->where('currency_id', $currency->id)
                ->lockForUpdate()
                ->first();

            // Create balance record if it doesn't exist
            if (!$balance) {
                $this->startBalanceChain($user);
                $balance = UserBalance::where('user_id', $user->id)
                    ->where('currency_id', $currency->id)
                    ->lockForUpdate()
                    ->first();
            }

            $balanceBefore = $balance->balance;
            $balanceAfter = $balanceBefore + $amount;

            // Validate balance doesn't go negative (unless explicitly allowed)
            if ($balanceAfter < 0 && !$this->isNegativeBalanceAllowed($type)) {
                throw new \InvalidArgumentException('Insufficient balance');
            }

            // Get the previous transaction hash for chain verification
            $previousTransaction = Transaction::where('user_id', $user->id)
                ->where('currency_id', $currency->id)
                ->orderBy('id', 'desc')
                ->first();

            $previousHash = $previousTransaction
                ? $previousTransaction->hash
                : hash('sha256', "genesis_{$user->id}_{$currency->id}");

            $createdAt = now();

            // Create the transaction record
            $transaction = new Transaction([
                'user_id' => $user->id,
                'currency_id' => $currency->id,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'type' => $type,
                'reference_id' => $referenceId,
                'meta' => $meta,
                'previous_hash' => $previousHash,
                'created_at' => $createdAt,
            ]);

            // Generate transaction hash
            $transaction->hash = $this->generateTransactionHash(
                $user->id,
                $currency->id,
                $amount,
                $balanceBefore,
                $balanceAfter,
                $type,
                $referenceId,
                $meta,
                $createdAt,
                $previousHash
            );

            $transaction->save();

            // Update the balance
            $balance->balance = $balanceAfter;
            $balance->balance_hash = $this->generateBalanceHash(
                $user->id,
                $currency->id,
                $balanceAfter,
                $transaction->id,
                $transaction->hash
            );
            $balance->updated_at = now();
            $balance->save();

            // Verify integrity
            if (!$this->verifyTransactionIntegrity($transaction)) {
                throw new \RuntimeException('Transaction integrity verification failed');
            }

            Log::info('Balance transaction processed', [
                'user_id' => $user->id,
                'currency_id' => $currency->id,
                'amount' => $amount,
                'type' => $type,
                'transaction_id' => $transaction->id,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
            ]);

            // Emit WebSocket event for balance update
            $this->emitBalanceUpdate($user, $currency, $balanceAfter);

            return $transaction;
        });
    }

    /**
     * Check if negative balance is allowed for a transaction type.
     */
    private function isNegativeBalanceAllowed(string $type): bool
    {
        $allowedTypes = ['adjustment', 'correction'];
        return in_array($type, $allowedTypes);
    }

    /**
     * Generate transaction hash according to specification.
     */
    private function generateTransactionHash(
        int $userId,
        int $currencyId,
        int $amount,
        int $balanceBefore,
        int $balanceAfter,
        string $type,
        ?string $referenceId,
        ?array $meta,
        $createdAt,
        string $previousHash
    ): string {
        $data = [
            'user_id' => $userId,
            'currency_id' => $currencyId,
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
            'type' => $type,
            'reference_id' => $referenceId,
            'meta' => $meta ? json_encode($meta) : null,
            'created_at' => $createdAt->format('Y-m-d H:i:s'),
            'previous_hash' => $previousHash,
        ];

        ksort($data);
        return hash('sha256', json_encode($data));
    }

    /**
     * Generate balance hash according to specification.
     */
    private function generateBalanceHash(
        int $userId,
        int $currencyId,
        int $balance,
        int $lastTxnId,
        string $lastTxnHash
    ): string {
        $data = [
            'user_id' => $userId,
            'currency_id' => $currencyId,
            'balance' => $balance,
            'last_txn_id' => $lastTxnId,
            'last_txn_hash' => $lastTxnHash,
        ];

        ksort($data);
        return hash('sha256', json_encode($data));
    }

    /**
     * Verify transaction integrity including hash chain.
     */
    public function verifyTransactionIntegrity(Transaction $transaction): bool
    {
        // Verify transaction hash
        $expectedHash = $this->generateTransactionHash(
            $transaction->user_id,
            $transaction->currency_id,
            $transaction->amount,
            $transaction->balance_before,
            $transaction->balance_after,
            $transaction->type,
            $transaction->reference_id,
            $transaction->meta,
            $transaction->created_at,
            $transaction->previous_hash
        );

        if (!hash_equals($expectedHash, $transaction->hash)) {
            Log::error('Transaction hash verification failed', [
                'transaction_id' => $transaction->id,
                'expected_hash' => $expectedHash,
                'actual_hash' => $transaction->hash
            ]);
            return false;
        }

        // Verify hash chain
        $previousTransaction = Transaction::where('user_id', $transaction->user_id)
            ->where('currency_id', $transaction->currency_id)
            ->where('id', '<', $transaction->id)
            ->orderBy('id', 'desc')
            ->first();

        if ($previousTransaction) {
            if (!hash_equals($previousTransaction->hash, $transaction->previous_hash)) {
                Log::error('Transaction chain verification failed', [
                    'transaction_id' => $transaction->id,
                    'expected_previous_hash' => $previousTransaction->hash,
                    'actual_previous_hash' => $transaction->previous_hash
                ]);
                return false;
            }
        } else {
            // First transaction should have genesis hash
            $expectedGenesisHash = hash('sha256', "genesis_{$transaction->user_id}_{$transaction->currency_id}");
            if (!hash_equals($expectedGenesisHash, $transaction->previous_hash)) {
                Log::error('Genesis transaction hash verification failed', [
                    'transaction_id' => $transaction->id,
                    'expected_genesis_hash' => $expectedGenesisHash,
                    'actual_previous_hash' => $transaction->previous_hash
                ]);
                return false;
            }
        }

        // Verify balance hash
        $balance = UserBalance::where('user_id', $transaction->user_id)
            ->where('currency_id', $transaction->currency_id)
            ->first();

        if (!$balance) {
            Log::error('Balance not found', ['transaction_id' => $transaction->id]);
            return false;
        }

        // Get the latest transaction for this user and currency to verify balance hash
        $latestTransaction = Transaction::where('user_id', $transaction->user_id)
            ->where('currency_id', $transaction->currency_id)
            ->orderBy('id', 'desc')
            ->first();

        $expectedBalanceHash = $this->generateBalanceHash(
            $balance->user_id,
            $balance->currency_id,
            $balance->balance,
            $latestTransaction->id,
            $latestTransaction->hash
        );

        if (!hash_equals($expectedBalanceHash, $balance->balance_hash)) {
            Log::error('Balance hash verification failed', [
                'transaction_id' => $transaction->id,
                'expected_balance_hash' => $expectedBalanceHash,
                'actual_balance_hash' => $balance->balance_hash
            ]);
            return false;
        }

        return true;
    }

    /**
     * Get all balances for a user (legacy method for compatibility).
     */
    public function getAllBalances(User $user): \Illuminate\Database\Eloquent\Collection
    {
        return UserBalance::where('user_id', $user->id)
            ->with('currency')
            ->get();
    }

    /**
     * Verify all transactions for a user and currency.
     */
    public function verifyUserTransactionChain(User $user, Currency $currency): bool
    {
        $transactions = Transaction::where('user_id', $user->id)
            ->where('currency_id', $currency->id)
            ->orderBy('id')
            ->get();

        foreach ($transactions as $transaction) {
            if (!$this->verifyTransactionIntegrity($transaction)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Emit WebSocket event for balance update.
     */
    private function emitBalanceUpdate(User $user, Currency $currency, int $newBalance): void
    {
        try {
            $sequence = $this->getNextSequence($user->id, $currency->id);

            broadcast(new \App\Events\BalanceUpdated(
                $user->id,
                $currency->code,
                $newBalance,
                $currency->fromMinorUnits($newBalance),
                $sequence
            ));
        } catch (\Exception $e) {
            Log::error('Failed to emit balance update event', [
                'user_id' => $user->id,
                'currency_code' => $currency->code,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
